#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# dependencies = [
#     "requests",
#     "rich",
#     "PyQt6",
#     "pyobjc-core",
#     "pyobjc-framework-Cocoa",
#     "Pillow",
#     "imagehash",
# ]
# ///

import os
import sys
import requests
from pathlib import Path
import re
import urllib.parse
import sqlite3
import hashlib
import imagehash
import subprocess
import tempfile
import shlex
import time
import shutil
from PIL import Image
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QTextEdit, QPushButton, QProgressBar,
    QFrame, QCompleter, QStyle, QCheckBox, QMessageBox,
    QLayout, QSizePolicy, QDialog, QGridLayout, QScrollArea,
    QRadioButton, QButtonGroup, QListWidget, QFileDialog, QMenuBar
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QStandardPaths, QStringListModel, QRect, QPoint, QSize, QMimeData
from PyQt6.QtGui import (
    QFont,
    QColor,
    QTextCursor,
    QAction,
    QDrag,
    QPixmap,
    QKeySequence,
)
# Attempt to import AppKit for macOS specific features like Dock icon
APPKIT_AVAILABLE = False
NSApplication = None
NSImage = None
try:
    from AppKit import NSApplication, NSImage
    APPKIT_AVAILABLE = True
except ImportError:
    pass # AppKit not available, likely not on macOS or pyobjc not installed

# Basis-Verzeichnis für Cosplay-Bilder
COSPLAY_DIR = Path("/Users/<USER>/Pictures/Anime/Cosplay")

# Hash-Datenbank Konfiguration (gleiche wie find-duplicate-cos.py)
HASH_CACHE_DIR = Path(QStandardPaths.writableLocation(QStandardPaths.StandardLocation.CacheLocation)) / "find-duplicate-cos"
HASH_DB_PATH = HASH_CACHE_DIR / "hashes.db"
TEMP_DIR = HASH_CACHE_DIR / "temp_images"
DEFAULT_HASH_THRESHOLD = 5

# Pillow Konfiguration
Image.MAX_IMAGE_PIXELS = None

class URLHistory:
    """Verwaltet die Historie der heruntergeladenen URLs mit SQLite"""

    def __init__(self, db_file: Path):
        self.db_file = db_file
        self._init_db()

    def _init_db(self):
        """Initialisiert die Datenbank und erstellt die Tabelle falls nötig"""
        self.db_file.parent.mkdir(parents=True, exist_ok=True)
        with sqlite3.connect(self.db_file) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS url_history (
                    url TEXT PRIMARY KEY,
                    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_added_at ON url_history(added_at)')

    def add_url(self, url: str) -> None:
        """Fügt eine normalisierte URL zur Historie hinzu"""
        normalized_url = DownloaderThread.process_image_url_static(url)
        with sqlite3.connect(self.db_file) as conn:
            conn.execute('''
                INSERT OR REPLACE INTO url_history (url, added_at)
                VALUES (?, CURRENT_TIMESTAMP)
            ''', (normalized_url,))

    def contains(self, url: str) -> bool:
        """Prüft, ob eine normalisierte URL in der Historie vorhanden ist"""
        normalized_url = DownloaderThread.process_image_url_static(url)
        with sqlite3.connect(self.db_file) as conn:
            result = conn.execute('SELECT 1 FROM url_history WHERE url = ?', (normalized_url,)).fetchone()
            return result is not None

    def clear(self) -> None:
        """Löscht die gesamte Historie"""
        with sqlite3.connect(self.db_file) as conn:
            conn.execute('DELETE FROM url_history')


class ImageHashManager:
    """Verwaltet die Hash-Datenbank für visuelle Duplikatserkennung"""

    def __init__(self, db_path: Path):
        self.db_path = db_path
        self.threshold = DEFAULT_HASH_THRESHOLD
        self._init_db()
        self._init_temp_dir()

    def _init_db(self):
        """Initialisiert die Hash-Datenbank (gleiche Struktur wie find-duplicate-cos.py)"""
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS images (
                    path TEXT PRIMARY KEY,
                    phash TEXT,
                    file_hash TEXT,
                    mtime REAL
                )
            ''')
            conn.commit()

    def _init_temp_dir(self):
        """Initialisiert das temporäre Verzeichnis"""
        TEMP_DIR.mkdir(parents=True, exist_ok=True)
        # Cleanup alte temporäre Dateien
        for old_file in TEMP_DIR.glob("*"):
            try:
                old_file.unlink()
            except:
                pass

    def calculate_hash_from_file(self, image_path: Path) -> str | None:
        """Berechnet den perceptual hash für eine lokale Bilddatei"""
        try:
            with Image.open(image_path) as img:
                return str(imagehash.phash(img))
        except Exception:
            return None

    def calculate_hash_from_url(self, url: str) -> tuple[str | None, Path | None]:
        """Lädt Bild temporär herunter und berechnet Hash. Gibt (hash, temp_path) zurück"""
        try:
            # Headers wie in find-duplicate-cos.py
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            }

            if "cdn.v2ph.com" in url:
                headers['Referer'] = 'https://www.v2ph.com/'

            if re.search(r'https?://([^.]+\.)*sinaimg\.cn', url):
                headers['Referer'] = 'https://weibo.com/'

            # Temporäre Datei erstellen
            timestamp = int(time.time())
            temp_filename = f"temp_hash_{timestamp}.jpg"
            temp_path = TEMP_DIR / temp_filename

            # Bild herunterladen
            response = requests.get(url, headers=headers, timeout=30, stream=True)
            response.raise_for_status()

            with open(temp_path, 'wb') as temp_file:
                for chunk in response.iter_content(chunk_size=8192):
                    temp_file.write(chunk)

            # Hash berechnen
            with Image.open(temp_path) as img:
                img.verify()  # Validierung

            with Image.open(temp_path) as img:
                phash = str(imagehash.phash(img))

            return phash, temp_path

        except Exception:
            return None, None

    def find_similar_images(self, target_hash: str) -> list[tuple[Path, int]]:
        """Findet ähnliche Bilder in der Datenbank. Gibt Liste von (path, distance) zurück"""
        if not target_hash:
            return []

        try:
            target_hash_obj = imagehash.hex_to_hash(target_hash)
        except Exception:
            return []

        similar_images = []

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT path, phash FROM images")

            for row in cursor.fetchall():
                db_path = Path(row[0])
                if not db_path.exists():
                    continue

                try:
                    db_hash = imagehash.hex_to_hash(row[1])
                    distance = target_hash_obj - db_hash
                    if distance <= self.threshold:
                        similar_images.append((db_path, distance))
                except Exception:
                    continue

        # Nach Ähnlichkeit sortieren (niedrigste Distanz zuerst)
        similar_images.sort(key=lambda x: x[1])
        return similar_images

    def add_image_to_db(self, image_path: Path):
        """Fügt ein Bild zur Hash-Datenbank hinzu"""
        if not image_path.exists():
            return

        try:
            phash = self.calculate_hash_from_file(image_path)
            if not phash:
                return

            file_hash = hashlib.md5(image_path.read_bytes()).hexdigest()
            mtime = image_path.stat().st_mtime

            with sqlite3.connect(self.db_path) as conn:
                conn.execute(
                    "INSERT OR REPLACE INTO images (path, phash, file_hash, mtime) VALUES (?, ?, ?, ?)",
                    (str(image_path), phash, file_hash, mtime)
                )
                conn.commit()
        except Exception:
            pass

    def cleanup_temp_file(self, temp_path: Path):
        """Löscht eine temporäre Datei"""
        try:
            if temp_path and temp_path.exists():
                temp_path.unlink()
        except Exception:
            pass

    def remove_image_from_db(self, image_path: Path):
        """Entfernt ein spezifisches Bild aus der Hash-Datenbank"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("DELETE FROM images WHERE path = ?", (str(image_path),))
                conn.commit()
        except Exception:
            pass

    def cleanup_database(self) -> tuple[int, int]:
        """Entfernt Einträge für nicht mehr existierende Dateien und gibt (gelöschte_anzahl, neue_gesamtanzahl) zurück."""
        deleted_count = 0
        total_count = 0
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT path FROM images")
                all_paths = [row[0] for row in cursor.fetchall()]

                for path_str in all_paths:
                    if not Path(path_str).exists():
                        cursor.execute("DELETE FROM images WHERE path = ?", (path_str,))
                        deleted_count += 1

                conn.commit()

                # Neue Gesamtanzahl ermitteln
                total_count = conn.execute("SELECT COUNT(*) FROM images").fetchone()[0]

        except Exception:
            pass

        return deleted_count, total_count


class LogWidget(QTextEdit):
    """Ein angepasstes QTextEdit für die Protokollausgabe"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setReadOnly(True)
        self.setMinimumHeight(200)

    def log(self, message, color=None):
        """Fügt eine Nachricht zum Log hinzu"""
        cursor = self.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        if color:
            self.setTextColor(QColor(color))
        else:
            self.setTextColor(QColor("#000000"))
        self.append(message)
        self.verticalScrollBar().setValue(self.verticalScrollBar().maximum())
        QApplication.processEvents()

    def log_success(self, message):
        """Protokolliert eine Erfolgsmeldung"""
        self.log(f"✅ {message}", "#2E7D32")
        
    def log_error(self, message):
        """Protokolliert eine Fehlermeldung"""
        self.log(f"❌ {message}", "#C62828")
        
    def log_info(self, message):
        """Protokolliert eine Informationsmeldung"""
        self.log(f"ℹ️ {message}", "#1565C0")
        
    def log_warning(self, message):
        """Protokolliert eine Warnmeldung"""
        self.log(f"⚠️ {message}", "#F9A825")

# -------- Flow layout and tag-input widgets ---------------------------------
class TagChip(QPushButton):
    """Draggable chip button."""
    def __init__(self, text: str, parent=None):
        super().__init__(text, parent)
        self.setFlat(True)
        self.setStyleSheet(
            """
            QPushButton { border:1px solid #888; border-radius:8px; padding:2px 6px; background:#ddd; }
            QPushButton:hover { background:#ccc; }
            """
        )
        self._drag_start = None

    # --- drag support -----------------------------------------------------
    def mousePressEvent(self, ev):
        if ev.button() == Qt.MouseButton.LeftButton:
            self._drag_start = ev.pos()
        super().mousePressEvent(ev)

    def mouseMoveEvent(self, ev):
        if (
            self._drag_start is not None
            and (ev.pos() - self._drag_start).manhattanLength() > QApplication.startDragDistance()
        ):
            drag = QDrag(self)
            mime = QMimeData()
            mime.setText(self.text())
            drag.setMimeData(mime)
            drag.exec(Qt.DropAction.MoveAction)
            self._drag_start = None
            # The chip can be deleted during the drag-and-drop operation,
            # so skip further processing to avoid accessing a deleted C++ object.
            return
        super().mouseMoveEvent(ev)


class FlowLayout(QLayout):
    """Simple flow layout (Qt example code, trimmed)."""
    def __init__(self, parent=None, margin=0, spacing=-1):
        super().__init__(parent)
        if parent is not None:
            self.setContentsMargins(margin, margin, margin, margin)
        self._h_spacing = spacing
        self._v_spacing = spacing
        self.itemList = []

    # --- QLayout interface --------------------------------------------------
    def addItem(self, item):                 self.itemList.append(item)
    def count(self):                         return len(self.itemList)
    def itemAt(self, i):                     return self.itemList[i] if 0 <= i < len(self.itemList) else None
    def takeAt(self, i):                     return self.itemList.pop(i) if 0 <= i < len(self.itemList) else None
    def expandingDirections(self):           return Qt.Orientation(0)
    def hasHeightForWidth(self):             return True
    def heightForWidth(self, w):             return self.doLayout(QRect(0, 0, w, 0), True)
    def setGeometry(self, r):                super().setGeometry(r); self.doLayout(r, False)
    def sizeHint(self):                      return self.minimumSize()

    def minimumSize(self):
        size = QSize()
        for item in self.itemList:
            size = size.expandedTo(item.minimumSize())
        m = self.contentsMargins().top()
        size += QSize(2 * m, 2 * m)
        return size

    # --- Internal -----------------------------------------------------------
    def doLayout(self, rect, test_only):
        x, y, line_height = rect.x(), rect.y(), 0
        for item in self.itemList:
            item_width = item.sizeHint().width()
            # Keep the edit field wide until chips consume space
            if item is self.itemList[-1] and item.widget() and isinstance(item.widget(), QLineEdit):
                item_width = max(item_width, rect.right() - x)
            next_x = x + item_width + self.horizontalSpacing()
            if next_x - self.horizontalSpacing() > rect.right() and line_height:
                x, y = rect.x(), y + line_height + self.verticalSpacing()
                next_x = x + item.sizeHint().width() + self.horizontalSpacing()
                line_height = 0
            if not test_only:
                size = item.sizeHint()
                if item_width != size.width():
                    size = QSize(item_width, size.height())
                item.setGeometry(QRect(QPoint(x, y), size))
            x = next_x
            line_height = max(line_height, item.sizeHint().height())
        return y + line_height - rect.y()

    def horizontalSpacing(self):
        return self._h_spacing if self._h_spacing >= 0 else self.smartSpacing(QStyle.PM_LayoutHorizontalSpacing)
    def verticalSpacing(self):
        return self._v_spacing if self._v_spacing >= 0 else self.smartSpacing(QStyle.PM_LayoutVerticalSpacing)
    def smartSpacing(self, pm):
        p = self.parent()
        if p is None: return self.spacing()
        cls = p.metaObject().className()
        if cls == 'QLayout':   return p.spacing()
        if cls == 'QWidget':   return p.style().pixelMetric(pm, None, p)
        return self.spacing()

class TagInputWidget(QWidget):
    """Widget that lets the user enter multiple tags (“chips”)."""
    def __init__(self, completer_model: QStringListModel | None = None, parent=None):
        super().__init__(parent)
        self.layout = FlowLayout(self, margin=2, spacing=4)
        self.line_edit = QLineEdit()
        self.line_edit.setFrame(False)
        # Allow the text field to expand – start full-width and shrink as chips appear
        self.line_edit.setMinimumWidth(0)
        self.line_edit.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.line_edit.installEventFilter(self)
        self.setAcceptDrops(True)
        if completer_model is not None:
            comp = QCompleter(completer_model, self)
            comp.setCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
            comp.setFilterMode(Qt.MatchFlag.MatchContains)
            self.line_edit.setCompleter(comp)
        self.layout.addWidget(self.line_edit)
        self._tags: list[str] = []

    # Public API -------------------------------------------------------------
    @property
    def tags(self) -> list[str]:
        return self._tags.copy()

    def clear(self):
        self._tags.clear()
        for i in reversed(range(self.layout.count())):
            item = self.layout.itemAt(i)
            w = item.widget() if item else None
            if w and w is not self.line_edit:
                self.layout.takeAt(i)
                w.deleteLater()
        self.line_edit.clear()

    def setFocus(self):
        self.line_edit.setFocus()

    def show_completer(self):
        if c := self.line_edit.completer():
            c.complete()

    # Internal --------------------------------------------------------------
    def eventFilter(self, source, event):
        if source is self.line_edit and event.type() == event.Type.KeyPress:
            if event.key() in (Qt.Key.Key_Return, Qt.Key.Key_Enter) or event.text() == ',':
                text = self.line_edit.text().strip().strip(',')
                if text:
                    self._add_tag(text)
                self.line_edit.clear()
                return True
            elif event.key() == Qt.Key.Key_Tab:
                text = self.line_edit.text().strip().strip(',')
                if text:
                    self._add_tag(text)
                self.line_edit.clear()
                self.focusNextChild()
                return True
        return super().eventFilter(source, event)

    def _add_tag(self, text: str):
        if text in self._tags:
            return
        self._tags.append(text)
        chip = TagChip(text, self)
        chip.clicked.connect(lambda _=False, t=text, w=chip: self._remove_tag(t, w))
        self.layout.addWidget(chip)

    def _remove_tag(self, text: str, widget: QWidget):
        if text in self._tags:
            self._tags.remove(text)
        widget.setParent(None)
        widget.deleteLater()

    # ---------- drag-and-drop (re-order chips) ----------------------------
    def dragEnterEvent(self, ev):
        if ev.mimeData().hasText():
            ev.acceptProposedAction()

    def dragMoveEvent(self, ev):
        if ev.mimeData().hasText():
            ev.acceptProposedAction()

    def dropEvent(self, ev):
        text = ev.mimeData().text()
        if text not in self._tags:
            return

        # current index of dragged chip
        current_idx = self._tags.index(text)

        # determine target index based on drop x-coordinate
        pos = ev.position().toPoint() if hasattr(ev, "position") else ev.pos()
        chip_widgets = [
            item.widget()
            for item in self.layout.itemList
            if item.widget() is not None and item.widget() is not self.line_edit
        ]

        target_idx = len(chip_widgets)  # default: append at end
        for idx, w in enumerate(chip_widgets):
            geo = w.geometry()
            if pos.y() < geo.bottom() and pos.x() < geo.center().x():
                target_idx = idx
                break

        if target_idx == current_idx:
            return

        # reorder lists
        self._tags.insert(target_idx, self._tags.pop(current_idx))
        self._rebuild_chips()
        ev.acceptProposedAction()

    # helper to rebuild chips from _tags
    def _rebuild_chips(self):
        # remove all chip widgets
        for i in reversed(range(self.layout.count())):
            item = self.layout.itemAt(i)
            w = item.widget() if item else None
            if w and w is not self.line_edit:
                self.layout.takeAt(i)
                w.deleteLater()

        # add chips in new order
        for t in self._tags:
            chip = TagChip(t, self)
            chip.clicked.connect(lambda _=False, tt=t, ww=chip: self._remove_tag(tt, ww))
            self.layout.addWidget(chip)

        # ensure line_edit is last
        self.layout.addWidget(self.line_edit)


class ClickableImageLabel(QLabel):
    """Ein klickbares QLabel für Bilder, das bei Doppelklick das Bild öffnet"""

    def __init__(self, image_path: Path, parent=None):
        super().__init__(parent)
        self.image_path = image_path
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self.setToolTip(f"Doppelklick zum Öffnen: {image_path}")

    def mouseDoubleClickEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            try:
                subprocess.Popen(['open', str(self.image_path)])
            except Exception:
                pass  # Fehler beim Öffnen ignorieren
        super().mouseDoubleClickEvent(event)

class ImageScanThread(QThread):
    """Thread für das Scannen und Hashen von Bildern im Hintergrund"""
    progress_signal = pyqtSignal(int, int)  # Aktuell, Gesamt
    status_signal = pyqtSignal(str, str)    # Nachricht, Typ (success, error, info, warning)
    finished_signal = pyqtSignal(int)       # Anzahl gescannte Bilder
    db_cleaned_signal = pyqtSignal()      # Signal, dass die DB-Bereinigung fertig ist

    def __init__(self, hash_manager, cosplay_dir):
        QThread.__init__(self)
        self.hash_manager = hash_manager
        self.cosplay_dir = cosplay_dir

    def run(self):
        """Scannt den Cosplay-Ordner nach neuen Bildern und fügt sie zur Hash-Datenbank hinzu"""
        if not self.cosplay_dir.exists():
            self.finished_signal.emit(0)
            return

        try:
            # Datenbank von nicht mehr existierenden Dateien bereinigen
            self.status_signal.emit("Bereinige Datenbank von gelöschten Dateien...", "info")
            deleted_count, new_total = self.hash_manager.cleanup_database()
            if deleted_count > 0:
                self.status_signal.emit(f"{deleted_count} Einträge für gelöschte Dateien aus Datenbank entfernt.", "info")
            
            # Signal senden, damit die Statusleiste sofort aktualisiert wird
            self.db_cleaned_signal.emit()

            # Alle Bilddateien im Cosplay-Ordner finden
            self.status_signal.emit("Suche nach Bilddateien...", "info")
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
            all_images = []
            for ext in image_extensions:
                all_images.extend(self.cosplay_dir.rglob(f"*{ext}"))
                all_images.extend(self.cosplay_dir.rglob(f"*{ext.upper()}"))

            # Prüfen, welche Bilder noch nicht in der Datenbank sind
            self.status_signal.emit("Prüfe Datenbank auf neue Bilder...", "info")
            new_images = []
            with sqlite3.connect(HASH_DB_PATH) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT path, mtime FROM images")
                db_files = {Path(row[0]): row[1] for row in cursor.fetchall()}

            for img_path in all_images:
                if img_path not in db_files or img_path.stat().st_mtime != db_files.get(img_path, 0):
                    new_images.append(img_path)

            total_images = len(new_images)
            if total_images == 0:
                self.status_signal.emit("Keine neuen Bilder zum Scannen gefunden.", "info")
                self.finished_signal.emit(0)
                return

            self.status_signal.emit(f"Scanne {total_images} neue/geänderte Bilder...", "info")

            # Bilder hashen mit Fortschrittsanzeige
            for index, img_path in enumerate(new_images):
                self.progress_signal.emit(index + 1, total_images)

                # Status-Update mit Prozent und Dateiname
                percent = int((index + 1) / total_images * 100)
                filename = img_path.name
                if len(filename) > 50:
                    filename = filename[:47] + "..."
                self.status_signal.emit(f"Scanne Bild {index + 1}/{total_images} ({percent}%): {filename}", "info")

                # Hash berechnen und zur Datenbank hinzufügen
                self.hash_manager.add_image_to_db(img_path)

                # Kurze Pause für UI-Updates
                time.sleep(0.01)

            self.status_signal.emit(f"Hash-Scan abgeschlossen. {total_images} Bilder verarbeitet.", "success")
            self.finished_signal.emit(total_images)

        except Exception as e:
            self.status_signal.emit(f"Fehler beim Scannen neuer Bilder: {e}", "error")
            self.finished_signal.emit(0)

# ---------------------------------------------------------------------------
class DownloaderThread(QThread):
    """Thread für das Herunterladen von Bildern im Hintergrund"""
    progress_signal = pyqtSignal(int, int)  # Aktuell, Gesamt
    status_signal = pyqtSignal(str, str)    # Nachricht, Typ (success, error, info, warning)
    finished_signal = pyqtSignal(int, int, list)  # Erfolgreiche, Gesamt, Erfolgreich heruntergeladene URLs
    duplicate_found_signal = pyqtSignal(str, list, str)  # URL, ähnliche Bilder (als Pfade), temp_hash
    file_processed_signal = pyqtSignal(str)    # Signal für erfolgreich verarbeitete lokale Dateien

    def __init__(self, sources, cosplayers, characters, hash_manager=None, check_duplicates=True, auto_compress=True, is_url_mode=True):
        QThread.__init__(self)
        self.sources = sources          # URLs oder lokale Dateipfade
        self.cosplayer = cosplayers     # list[str]
        self.character = characters     # list[str]
        self.is_url_mode = is_url_mode  # True für URLs, False für lokale Dateien
        self.successful_urls = []       # URLs die erfolgreich heruntergeladen wurden

        self.hash_manager = hash_manager  # ImageHashManager instance
        self.check_duplicates = check_duplicates  # Ob Duplikatsprüfung aktiviert ist
        self.auto_compress = auto_compress  # Ob automatische Komprimierung aktiviert ist
        self.user_decisions = {}        # Cache für Benutzerentscheidungen bei Duplikaten
        self.session_hashes = {}        # Cache für bereits berechnete Hashes in dieser Sitzung
        self.skip_all_mode = False        # Modus für "Alle Überspringen"
        
    def run(self):
        successful = 0
        total = len(self.sources)

        for index, source in enumerate(self.sources):
            is_mp4 = not self.is_url_mode and Path(source).suffix.lower() == '.mp4'

            if self.is_url_mode:
                self.status_signal.emit(f"Verarbeite Bild {index + 1}/{total}", "info")
            else:
                if is_mp4:
                    self.status_signal.emit(f"Sortiere Video {index + 1}/{total} ein", "info")
                else:
                    self.status_signal.emit(f"Sortiere Datei {index + 1}/{total} ein", "info")

            # Prüfe auf Duplikate, wenn aktiviert
            should_skip = False
            temp_path = None

            # Wenn "Alle Überspringen" aktiv ist, direkt überspringen
            if self.skip_all_mode:
                self.status_signal.emit(f"Überspringe Bild ('Alle Überspringen' aktiv)", "info")
                self.progress_signal.emit(index + 1, total)
                continue

            if self.check_duplicates and self.hash_manager and not is_mp4:

                if self.is_url_mode:
                    decision_key = DownloaderThread.process_image_url_static(source)
                else:
                    decision_key = source

                # Prüfe zuerst, ob der Benutzer bereits eine Entscheidung getroffen hat
                if decision_key in self.user_decisions:
                    if self.user_decisions[decision_key] == "skip":
                        self.status_signal.emit(f"Überspringe Bild (Benutzerentscheidung)", "info")
                        should_skip = True
                else:
                    # Hash berechnen und Duplikate suchen
                    self.status_signal.emit("Prüfe auf visuelle Duplikate...", "info")
                    try:
                        # Prüfe zuerst den Session-Cache
                        cache_key = decision_key
                        if cache_key in self.session_hashes:
                            image_hash = self.session_hashes[cache_key]
                            temp_path = None
                        else:
                            # Hash neu berechnen
                            if self.is_url_mode:
                                processed_url = self.process_image_url(source)
                                image_hash, temp_path = self.hash_manager.calculate_hash_from_url(processed_url)
                            else:
                                image_hash = self.hash_manager.calculate_hash_from_file(Path(source))
                                temp_path = None

                            # Hash im Session-Cache speichern
                            if image_hash:
                                self.session_hashes[cache_key] = image_hash

                        if image_hash:
                            similar_images = self.hash_manager.find_similar_images(image_hash)
                            if similar_images:
                                similar_paths = [str(path) for path, distance in similar_images]
                                self.duplicate_found_signal.emit(source, similar_paths, image_hash)

                                while decision_key not in self.user_decisions:
                                    time.sleep(0.1)

                                decision = self.user_decisions[decision_key]
                                if decision == "skip":
                                    self.status_signal.emit(f"Überspringe Bild (ähnliche Bilder gefunden)", "warning")
                                    should_skip = True
                                elif decision == "overwrite":
                                    self.status_signal.emit(f"Überschreibe ähnliche Bilder...", "info")
                                    for path_str, distance in similar_images:
                                        if str(Path(path_str).resolve()) == str(Path(source).resolve()):
                                            self.status_signal.emit(f"Überspringe Quelldatei: {Path(path_str).name}", "info")
                                            continue
                                        try:
                                            Path(path_str).unlink()
                                            self.status_signal.emit(f"Gelöscht: {Path(path_str).name}", "info")
                                            if self.hash_manager:
                                                self.hash_manager.remove_image_from_db(Path(path_str))
                                        except Exception as e:
                                            self.status_signal.emit(f"Fehler beim Löschen von {Path(path_str).name}: {e}", "warning")
                    except Exception as e:
                        self.status_signal.emit(f"Fehler bei Duplikatsprüfung: {e}", "warning")

            if should_skip:
                # Temporäre Datei aufräumen falls vorhanden
                if temp_path and self.hash_manager:
                    self.hash_manager.cleanup_temp_file(temp_path)

                if not self.is_url_mode:
                    self.status_signal.emit(f"Datei übersprungen: {Path(source).name}", "info")
                else:
                    self.status_signal.emit(f"URL übersprungen", "info")

                self.progress_signal.emit(index + 1, total)
                continue

            # Dateinamen erstellen
            cosplayer_part = ", ".join(self.cosplayer) if isinstance(self.cosplayer, list) else str(self.cosplayer)
            character_part = ", ".join(self.character) if self.character else ""
            filename_base = f"{cosplayer_part} - [{character_part}]" if character_part else cosplayer_part

            # Dateiendung anpassen
            if is_mp4:
                extension = '.mp4'
            else:
                extension = '.jpg'

            # Pfad mit Basis-Dateinamen
            base_path = COSPLAY_DIR / f"{filename_base}{extension}"

            # Eindeutigen Dateinamen finden (nach möglichem Überschreiben erneut prüfen)
            save_path = self.get_unique_filename(base_path)

            # Bild herunterladen oder verschieben
            try:
                if self.is_url_mode:
                    processed_url = self.process_image_url(source)
                    if processed_url != source:
                        self.status_signal.emit("URL angepasst: Original-Qualität wird verwendet", "info")

                    final_path = self.download_image(processed_url, save_path)
                    action_text = "heruntergeladen"
                else:
                    final_path = self.move_local_file(source, save_path)
                    action_text = "einsortiert"

                file_size = final_path.stat().st_size
                size_str = self.format_file_size(file_size)

                self.status_signal.emit(f"Datei {action_text}: {final_path.name} ({size_str})", "success")

                # Automatische Komprimierung falls aktiviert und nötig
                if self.auto_compress and not is_mp4 and self.should_compress_image(source, final_path):
                    self.status_signal.emit("Komprimiere Bild...", "info")
                    compress_success, original_size, compressed_size, reduction, error_msg = self.compress_image(final_path)
                    if compress_success:
                        original_str = self.format_file_size(original_size)
                        compressed_str = self.format_file_size(compressed_size)
                        self.status_signal.emit(f"Komprimierung: {original_str} → {compressed_str} ({reduction:.1f}% Reduktion)".replace('.', ','), "success")
                    else:
                        self.status_signal.emit(f"Komprimierung fehlgeschlagen: {error_msg}", "error")

                # Überprüfen, ob das Bild korrekt gespeichert wurde
                if is_mp4:
                    if final_path.exists() and final_path.stat().st_size > 0:
                        successful += 1
                    else:
                        self.status_signal.emit(f"Verifizierung für MP4 fehlgeschlagen: Datei nicht korrekt gespeichert.", "error")
                else:
                    success, message = self.verify_image(final_path)
                    if not success:
                        self.status_signal.emit(f"Verifizierung fehlgeschlagen: {message}", "error")
                    else:
                        successful += 1
                        if self.is_url_mode:
                            self.successful_urls.append(source)  # Nur erfolgreich heruntergeladene URLs merken
                        else:
                            self.file_processed_signal.emit(source) # Signal für verarbeitete lokale Datei senden

                        # Hash zur Datenbank hinzufügen - SOFORT nach erfolgreichem Download
                        # damit nachfolgende Downloads in derselben Sitzung das Bild als Duplikat erkennen
                        if self.hash_manager:
                            self.hash_manager.add_image_to_db(final_path)

            except Exception as e:
                if self.is_url_mode:
                    self.status_signal.emit(f"Fehler beim Herunterladen des Bildes: {e}", "error")
                else:
                    self.status_signal.emit(f"Fehler beim Einsortieren der Datei: {e}", "error")
            finally:
                # Temporäre Datei aufräumen falls vorhanden
                if temp_path and self.hash_manager:
                    self.hash_manager.cleanup_temp_file(temp_path)

            self.progress_signal.emit(index + 1, total)

            # Kurze Pause, um die UI zu aktualisieren
            time.sleep(0.1)

        # Fertig
        self.finished_signal.emit(successful, total, self.successful_urls)

    def download_image(self, url, save_path):
        """Lädt ein Bild von einer URL herunter und speichert es"""
        headers = {}
        if "cdn.v2ph.com" in url:
            headers["Referer"] = "https://www.v2ph.com/"

        # Set referer for sinaimg.cn (including subdomains)
        if re.search(r'https?://([^.]+\.)*sinaimg\.cn', url):
            headers["Referer"] = "https://weibo.com/"
            
        response = requests.get(url, headers=headers, stream=True, timeout=30)
        response.raise_for_status()
        
        with open(save_path, 'wb') as file:
            for chunk in response.iter_content(chunk_size=8192):
                file.write(chunk)
        
        return save_path

    def move_local_file(self, source_path, target_path):
        """Verschiebt eine lokale Datei zum Zielort"""
        shutil.move(source_path, target_path)
        return target_path

    def process_image_url(self, url):
        """Verarbeitet URLs für den Download (nur Twitter URLs werden angepasst)"""
        return DownloaderThread.process_image_url_for_download(url)

    @staticmethod
    def process_image_url_for_download(url):
        """Verarbeitet URLs für den Download - nur Twitter URLs werden angepasst, Instagram bleibt unverändert"""
        if "pbs.twimg.com" in url:
            match = re.search(r'(\.[a-zA-Z0-9]+):(small|medium|large|thumb)$', url)
            if match:
                base_url_part = url[:match.start(2)]
                return f"{base_url_part}orig"
            parsed_url = urllib.parse.urlparse(url)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            if "name" in query_params and query_params["name"][0] != "orig":
                query_params["name"] = ["orig"]
                new_query = urllib.parse.urlencode(query_params, doseq=True)
                new_url = urllib.parse.urlunparse((
                    parsed_url.scheme,
                    parsed_url.netloc,
                    parsed_url.path,
                    parsed_url.params,
                    new_query,
                    parsed_url.fragment
                ))
                return new_url
        # Instagram URLs are NOT modified for download to avoid 403 errors
        return url

    @staticmethod
    def process_image_url_static(url):
        """Verarbeitet URLs für die Datenbank-Normalisierung (für History und Duplikatserkennung)"""
        if "pbs.twimg.com" in url:
            # Strip query parameters and :orig suffix for database storage
            parsed_url = urllib.parse.urlparse(url)
            path = parsed_url.path

            # Remove :orig, :small, :medium, :large, :thumb suffixes
            path = re.sub(r':(orig|small|medium|large|thumb)$', '', path)

            # Remove file extensions (.jpg, .jpeg, .png, .gif, .webp) for normalization
            path = re.sub(r'\.(jpg|jpeg|png|gif|webp)$', '', path, flags=re.IGNORECASE)

            # Return URL without query parameters, size suffixes, and file extensions
            return urllib.parse.urlunparse((
                parsed_url.scheme,
                parsed_url.netloc,
                path,
                parsed_url.params,
                "",  # Remove query string
                parsed_url.fragment
            ))
        elif "cdninstagram.com" in url:
            # Instagram CDN URLs: Strip query parameters for database storage/lookup
            parsed_url = urllib.parse.urlparse(url)
            return urllib.parse.urlunparse((
                parsed_url.scheme,
                parsed_url.netloc,
                parsed_url.path,
                parsed_url.params,
                "",  # Remove query string
                parsed_url.fragment
            ))
        return url
    
    def get_unique_filename(self, base_path):
        """Findet einen eindeutigen Dateinamen durch Anhängen von Nummern"""
        directory = base_path.parent
        stem = base_path.stem
        suffix = base_path.suffix
        
        # Prüfe ob die Basisdatei (unabhängig von Groß-/Kleinschreibung) existiert
        base_exists = any(f.name.lower() == base_path.name.lower() for f in directory.iterdir() if f.is_file())
        
        # Alle existierenden nummerierten Versionen finden (alle Endungen berücksichtigen)
        pattern = re.compile(rf"^{re.escape(stem)} (\d+)(?:{re.escape(suffix)}|\.[^\.]+)$", re.IGNORECASE)
        existing_numbers = []
        
        for file in directory.iterdir():
            if not file.is_file():
                continue
            match = pattern.match(file.name)
            if match:
                existing_numbers.append(int(match.group(1)))
        
        # Wenn die Basisdatei existiert, behandle sie als "01"
        if base_exists:
            existing_numbers.append(1)
        
        # Wenn keine nummerierten Versionen existieren und die Basisdatei nicht existiert,
        # können wir die Basisdatei verwenden
        if not existing_numbers:
            return base_path
        
        # Nächste verfügbare Nummer finden
        next_num = 1
        while next_num in existing_numbers:
            next_num += 1
        
        return directory / f"{stem} {next_num:02d}{suffix}"
    
    def get_image_type_from_magic_bytes(self, file_path):
        """Prüft den Bildtyp anhand der Magic Bytes"""
        # Magic Bytes für gängige Bildformate
        magic_bytes = {
            b'\xFF\xD8\xFF': 'jpeg',            # JPEG
            b'\x89\x50\x4E\x47': 'png',         # PNG
            b'\x47\x49\x46\x38': 'gif',         # GIF
            b'\x52\x49\x46\x46': 'webp',        # WEBP (beginnt mit "RIFF")
            b'\x42\x4D': 'bmp',                 # BMP
            b'\x49\x49\x2A\x00': 'tiff',        # TIFF (little endian)
            b'\x4D\x4D\x00\x2A': 'tiff',        # TIFF (big endian)
        }
        
        # Die ersten 8 Bytes der Datei lesen
        with open(file_path, 'rb') as f:
            header = f.read(8)
        
        # Prüfen, ob die Bytes mit einem bekannten Format übereinstimmen
        for magic, img_type in magic_bytes.items():
            if header.startswith(magic):
                return img_type
                
        return None

    def verify_image(self, file_path):
        """Überprüft, ob das Bild korrekt gespeichert wurde"""
        if not file_path.exists():
            return False, "Datei existiert nicht"
        
        file_size = file_path.stat().st_size
        if file_size == 0:
            return False, "Datei hat Größe 0"
        
        # Prüfe den Bildtyp mit Magic Bytes
        img_format = self.get_image_type_from_magic_bytes(file_path)
        if not img_format:
            return False, "Datei ist kein bekanntes Bildformat"
        
        # Formatiere die Ausgabe
        extension = file_path.suffix.lower()[1:]  # Ohne Punkt
        
        if img_format != extension and not (img_format == 'jpeg' and extension == 'jpg'):
            return True, f"Bild wurde gespeichert ({file_size} Bytes), aber der tatsächliche Typ ist {img_format}, nicht {extension}"
        
        return True, f"Bild wurde korrekt als {img_format.upper()} gespeichert ({file_size} Bytes)"

    def format_file_size(self, size_in_bytes):
        """Formatiert die Dateigröße in KB, MB oder GB"""
        if size_in_bytes < 1024 * 1024:  # Kleiner als 1 MB
            return f"{size_in_bytes / 1024:.1f} KB".replace('.', ',')
        elif size_in_bytes < 1024 * 1024 * 1024:  # Kleiner als 1 GB
            return f"{size_in_bytes / (1024 * 1024):.1f} MB".replace('.', ',')
        else:
            return f"{size_in_bytes / (1024 * 1024 * 1024):.2f} GB".replace('.', ',')

    def should_compress_image(self, source, file_path):
        """Prüft, ob ein Bild komprimiert werden sollte"""
        # Mindestgröße: 0.9 MB
        min_size = int(0.9 * 1024 * 1024)
        if file_path.stat().st_size < min_size:
            return False

        # Nicht komprimieren wenn von Twitter oder Instagram (nur bei URLs)
        if self.is_url_mode and ("pbs.twimg.com" in source or "cdninstagram.com" in source):
            return False

        return True

    def compress_image(self, file_path, quality=70):
        """Komprimiert ein Bild mit cjpeg (gleiche Einstellungen wie jpeg-compressor.py)"""
        temp_output = None
        temp_ppm = None
        try:
            original_size = file_path.stat().st_size

            # Temporäre Datei für komprimiertes Bild
            temp_output = Path(tempfile.mktemp(suffix='.jpg'))

            # Absoluter Pfad für sichere Shell-Kommandos
            abs_file_path = file_path.resolve()

            if file_path.suffix.lower() in ('.jpg', '.jpeg'):
                # JPEG: djpeg | cjpeg
                subprocess.run(
                    ["sh", "-c", f"djpeg {shlex.quote(str(abs_file_path))} | cjpeg -quality {quality} -outfile {shlex.quote(str(temp_output))}"],
                    check=True,
                    capture_output=True,
                    text=True
                )
            else:
                # Andere Formate: PIL -> PPM -> cjpeg
                with Image.open(file_path) as img:
                    # Zu RGB konvertieren falls nötig
                    if img.mode != 'RGB':
                        img = img.convert('RGB')

                    temp_ppm = Path(tempfile.mktemp(suffix='.ppm'))
                    img.save(temp_ppm)

                    # cjpeg verwenden
                    subprocess.run(
                        ["sh", "-c", f"cjpeg -quality {quality} {shlex.quote(str(temp_ppm))} > {shlex.quote(str(temp_output))}"],
                        check=True,
                        capture_output=True,
                        text=True
                    )

                    # Temporäre PPM-Datei löschen
                    temp_ppm.unlink()

            # Komprimierte Datei über Original kopieren
            compressed_size = temp_output.stat().st_size
            temp_output.replace(file_path)

            # Komprimierungsstatistiken berechnen
            reduction_percent = (1 - compressed_size / original_size) * 100

            return True, original_size, compressed_size, reduction_percent, None

        except subprocess.CalledProcessError as e:
            error_message = f"cjpeg/djpeg Fehler: {e.stderr.strip()}"
            # Bei Fehler temporäre Dateien aufräumen
            try:
                if temp_output and temp_output.exists():
                    temp_output.unlink()
                if temp_ppm and temp_ppm.exists():
                    temp_ppm.unlink()
            except:
                pass
            return False, 0, 0, 0, error_message
        except Exception as e:
            # Bei Fehler temporäre Dateien aufräumen
            try:
                if temp_output and temp_output.exists():
                    temp_output.unlink()
                if temp_ppm and temp_ppm.exists():
                    temp_ppm.unlink()
            except:
                pass
            return False, 0, 0, 0, str(e)

class CosplayDownloaderApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Cosplay Bild-Downloader")
        self.setMinimumSize(600, 500)
        
        # Historien-Listen
        self.cosplayer_history = []
        self.character_history = []
        
        # Pfad zur History-Datei
        self.app_name = "dl-cos-gui"
        cache_dir = Path(QStandardPaths.writableLocation(QStandardPaths.StandardLocation.CacheLocation))
        cache_dir.mkdir(parents=True, exist_ok=True)
        self.history_file = cache_dir / "history.json"
        
        # Zentrales Widget und Layout erstellen
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout(self.central_widget)
        self.layout.setSpacing(10)
        self.layout.setContentsMargins(20, 20, 20, 20)
        
        # Completer Modelle initialisieren
        self.cosplayer_completer_model = QStringListModel(self)
        self.character_completer_model = QStringListModel(self)

        # Duplikatsprüfung Checkbox (muss vor init_ui() erstellt werden)
        self.check_duplicates_cb = QCheckBox("Visuelle Duplikatsprüfung aktivieren")
        self.check_duplicates_cb.setChecked(True)  # Standard: aktiviert

        # Automatische Komprimierung Checkbox
        self.auto_compress_cb = QCheckBox("Automatische Komprimierung (>0,9 MB, außer Twitter/Instagram)")
        self.auto_compress_cb.setChecked(True)  # Standard: aktiviert

        # Erstelle die UI-Elemente
        self.init_ui()

        # Sicherstellen, dass das Verzeichnis existiert
        COSPLAY_DIR.mkdir(parents=True, exist_ok=True)

        # Status-Bar initialisieren
        self.statusBar().showMessage("Bereit")

        # Status-Bar-Widgets für zusätzliche Infos
        self.image_count_label = QLabel("Bilder: ...")
        self.db_size_label = QLabel("DB Größe: ...")
        self.statusBar().addPermanentWidget(self.image_count_label)
        self.statusBar().addPermanentWidget(self.db_size_label)

        # Downloader-Thread
        self.downloader_thread = None

        # Image-Scan-Thread
        self.image_scan_thread = None

        # URL-Historie initialisieren
        history_dir = Path(QStandardPaths.writableLocation(QStandardPaths.StandardLocation.CacheLocation))
        self.url_history = URLHistory(history_dir / "url_history.db")

        # Hash-Manager für Duplikatserkennung initialisieren
        self.hash_manager = ImageHashManager(HASH_DB_PATH)

        # Force-Download Checkbox
        self.force_download_cb = QCheckBox("Download erzwingen (auch wenn bereits heruntergeladen)")
        self.force_download_cb.setChecked(False)
        # self.layout.addWidget(self.force_download_cb)

        # Beim Start neue Bilder scannen und hashen (im Hintergrund) - NACH UI-Initialisierung
        self.scan_for_new_images()

        # Initiales Update der Statusleiste
        self.update_status_bar_info()

    def init_ui(self):
        # Überschrift
        header_label = QLabel("Cosplay Bild-Downloader")
        header_font = QFont("Helvetica", 18, QFont.Weight.Bold)
        header_label.setFont(header_font)
        header_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout.addWidget(header_label)
        
        # Container für Eingabefelder
        input_frame = QFrame()
        input_layout = QVBoxLayout(input_frame)
        input_layout.setContentsMargins(15, 15, 15, 15)
        
        # Zielverzeichnis (hardcoded, nicht änderbar)
        dir_layout = QHBoxLayout()
        dir_label = QLabel("Ziel:")
        dir_label.setFixedWidth(80)
        self.dir_input = QLineEdit(str(COSPLAY_DIR))
        self.dir_input.setReadOnly(True)
        dir_layout.addWidget(dir_label)
        dir_layout.addWidget(self.dir_input)
        input_layout.addLayout(dir_layout)
        
        # Cosplayer Eingabe
        cosplayer_layout = QHBoxLayout()
        cosplayer_label = QLabel("Cosplayer:")
        cosplayer_label.setFixedWidth(80)
        self.cosplayer_input = TagInputWidget(self.cosplayer_completer_model, self)
        self.cosplayer_input.line_edit.setPlaceholderText("Name des Cosplayers")
        self.cosplayer_completer = self.cosplayer_input.line_edit.completer()
        
        # Pfeil-Aktion für Cosplayer-Eingabefeld
        cosplayer_arrow_action = QAction(self.style().standardIcon(QStyle.StandardPixmap.SP_ArrowDown), "", self)
        cosplayer_arrow_action.triggered.connect(self.show_cosplayer_history_popup)
        self.cosplayer_input.line_edit.addAction(cosplayer_arrow_action, QLineEdit.ActionPosition.TrailingPosition)
        
        cosplayer_layout.addWidget(cosplayer_label)
        cosplayer_layout.addWidget(self.cosplayer_input)
        input_layout.addLayout(cosplayer_layout)
        
        # Character Eingabe
        character_layout = QHBoxLayout()
        character_label = QLabel("Figur:")
        character_label.setFixedWidth(80)
        self.character_input = TagInputWidget(self.character_completer_model, self)
        self.character_input.line_edit.setPlaceholderText("Name der Figur (optional)")
        self.character_completer = self.character_input.line_edit.completer()

        # Pfeil-Aktion für Charakter-Eingabefeld
        character_arrow_action = QAction(self.style().standardIcon(QStyle.StandardPixmap.SP_ArrowDown), "", self)
        character_arrow_action.triggered.connect(self.show_character_history_popup)
        self.character_input.line_edit.addAction(character_arrow_action, QLineEdit.ActionPosition.TrailingPosition)
        
        character_layout.addWidget(character_label)
        character_layout.addWidget(self.character_input)
        input_layout.addLayout(character_layout)
        
        # Eingabemodus wählen (URLs oder lokale Dateien)
        mode_layout = QHBoxLayout()
        mode_label = QLabel("Eingabemodus:")
        self.url_mode_rb = QRadioButton("URLs")
        self.file_mode_rb = QRadioButton("Lokale Dateien")
        self.url_mode_rb.setChecked(True)  # Standard: URLs

        # Radio-Buttons beim Durchtabben überspringen
        self.url_mode_rb.setFocusPolicy(Qt.FocusPolicy.NoFocus)
        self.file_mode_rb.setFocusPolicy(Qt.FocusPolicy.NoFocus)

        # Radio Button Gruppe
        self.mode_group = QButtonGroup()
        self.mode_group.addButton(self.url_mode_rb)
        self.mode_group.addButton(self.file_mode_rb)
        self.mode_group.buttonClicked.connect(self.on_mode_changed)

        mode_layout.addWidget(mode_label)
        mode_layout.addWidget(self.url_mode_rb)
        mode_layout.addWidget(self.file_mode_rb)
        mode_layout.addStretch()
        input_layout.addLayout(mode_layout)

        # URL Eingabefeld
        url_layout = QVBoxLayout()
        self.url_label = QLabel("Bild-URLs:")
        self.url_input = QTextEdit()
        self.url_input.setPlaceholderText("Eine oder mehrere URLs eingeben (durch Leerzeichen, Kommas oder Zeilenumbrüche getrennt)")
        self.url_input.setMinimumHeight(150)
        self.url_input.installEventFilter(self)  # Event-Filter installieren
        url_layout.addWidget(self.url_label)
        url_layout.addWidget(self.url_input)

        # Dateiauswahl-Bereich (initial versteckt)
        file_layout = QVBoxLayout()
        self.file_label = QLabel("Lokale Bilddateien:")

        file_button_layout = QHBoxLayout()
        self.select_files_btn = QPushButton("Dateien auswählen...")
        self.select_files_btn.clicked.connect(self.select_files)
        self.clear_files_btn = QPushButton("Liste leeren")
        self.clear_files_btn.clicked.connect(self.clear_selected_files)
        file_button_layout.addWidget(self.select_files_btn)
        file_button_layout.addWidget(self.clear_files_btn)
        file_button_layout.addStretch()

        self.selected_files_list = QListWidget()
        self.selected_files_list.setMinimumHeight(150)

        file_layout.addWidget(self.file_label)
        file_layout.addLayout(file_button_layout)
        file_layout.addWidget(self.selected_files_list)

        # Beide Bereiche zum Layout hinzufügen
        input_layout.addLayout(url_layout)
        input_layout.addLayout(file_layout)

        # Initial nur URL-Bereich anzeigen
        self.url_widgets = [self.url_label, self.url_input]
        self.file_widgets = [self.file_label, self.select_files_btn, self.clear_files_btn, self.selected_files_list]
        self.on_mode_changed()  # Initial setup
        
        # Optionen
        options_layout = QHBoxLayout()
        options_layout.addWidget(self.check_duplicates_cb)
        options_layout.addWidget(self.auto_compress_cb)
        options_layout.addStretch()
        input_layout.addLayout(options_layout)

        # Buttons
        button_layout = QHBoxLayout()
        self.download_btn = QPushButton("Herunterladen")
        self.download_btn.clicked.connect(self.start_download)

        self.clear_btn = QPushButton("Leeren")
        self.clear_btn.clicked.connect(self.clear_fields)

        button_layout.addWidget(self.clear_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.download_btn)
        input_layout.addLayout(button_layout)
        
        self.layout.addWidget(input_frame)
        
        # Fortschrittsbalken
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.layout.addWidget(self.progress_bar)
        
        # Log-Bereich
        log_label = QLabel("Protokoll:")
        self.layout.addWidget(log_label)
        
        self.log_widget = LogWidget()
        self.layout.addWidget(self.log_widget)
        
        # Historie laden und Completer füllen
        self.load_history()
        self.update_completer_models()

        # Autofokus auf Cosplayer-Eingabe setzen
        self.cosplayer_input.setFocus()

    def format_file_size(self, size_in_bytes):
        """Formatiert die Dateigröße in KB, MB oder GB"""
        if size_in_bytes < 1024 * 1024:  # Kleiner als 1 MB
            return f"{size_in_bytes / 1024:.1f} KB".replace('.', ',')
        elif size_in_bytes < 1024 * 1024 * 1024:  # Kleiner als 1 GB
            return f"{size_in_bytes / (1024 * 1024):.1f} MB".replace('.', ',')
        else:
            return f"{size_in_bytes / (1024 * 1024 * 1024):.2f} GB".replace('.', ',')

    def calculate_folder_size(self, folder_path):
        """Berechnet die Gesamtgröße aller Dateien in einem Ordner"""
        total_size = 0
        try:
            for file_path in folder_path.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception:
            # Bei Fehlern 0 zurückgeben
            pass
        return total_size

    def update_status_bar_info(self):
        """Aktualisiert die Informationen in der Statusleiste (Bildanzahl, Ordnergröße, DB-Größe)."""
        # Bildanzahl aus der Datenbank holen und Ordnergröße berechnen
        try:
            with sqlite3.connect(self.hash_manager.db_path) as conn:
                count = conn.execute("SELECT COUNT(*) FROM images").fetchone()[0]

                # Ordnergröße berechnen
                folder_size = self.calculate_folder_size(COSPLAY_DIR)
                folder_size_str = self.format_file_size(folder_size)

                self.image_count_label.setText(f"Bilder: {count} ({folder_size_str})")
        except Exception as e:
            self.image_count_label.setText("Bilder: Fehler")
            self.log_widget.log_warning(f"Konnte Bildanzahl oder Ordnergröße nicht laden: {e}")

        # Größe der Hash-Datenbank-Datei abrufen
        try:
            db_size = HASH_DB_PATH.stat().st_size
            size_str = self.downloader_thread.format_file_size(db_size) if self.downloader_thread else f"{db_size / (1024*1024):.1f} MB".replace('.', ',')
            self.db_size_label.setText(f"DB Größe: {size_str}")
        except FileNotFoundError:
            self.db_size_label.setText("DB Größe: N/A")
        except Exception as e:
            self.db_size_label.setText("DB Größe: Fehler")
            self.log_widget.log_warning(f"Konnte DB-Größe nicht laden: {e}")

    def update_dock_progress(self, current, total):
        """Aktualisiert die Fortschrittsanzeige im macOS Dock-Symbol"""
        if not APPKIT_AVAILABLE or total <= 0:
            return

        try:
            progress = int((current / total) * 100)
            ns_app = NSApplication.sharedApplication()
            dock_tile = ns_app.dockTile()
            dock_tile.setBadgeLabel_(f"{progress}%")
        except Exception as e:
            # Fehler beim Setzen des Dock-Badges ignorieren
            pass

    def clear_dock_progress(self):
        """Entfernt die Fortschrittsanzeige vom macOS Dock-Symbol"""
        if not APPKIT_AVAILABLE:
            return

        try:
            ns_app = NSApplication.sharedApplication()
            dock_tile = ns_app.dockTile()
            dock_tile.setBadgeLabel_(None)
        except Exception as e:
            # Fehler beim Entfernen des Dock-Badges ignorieren
            pass

    def eventFilter(self, source, event):
        """Fängt Tastatureingaben ab, um spezielle Aktionen auszulösen."""
        if event.type() == event.Type.KeyPress:
            if source is self.url_input:
                if event.key() in (Qt.Key.Key_Return, Qt.Key.Key_Enter) and \
                   (event.modifiers() == Qt.KeyboardModifier.MetaModifier or event.modifiers() == Qt.KeyboardModifier.ControlModifier): # CMD/STRG + ENTER
                    self.start_download()
                    return True  # Event wurde behandelt
            elif source is self.cosplayer_input:
                if event.key() == Qt.Key.Key_Down:
                    self.cosplayer_completer.complete()
                    return True # Event wurde behandelt
            elif source is self.character_input:
                if event.key() == Qt.Key.Key_Down:
                    self.character_completer.complete()
                    return True # Event wurde behandelt
        return super().eventFilter(source, event)

    def on_mode_changed(self):
        """Wird aufgerufen, wenn der Eingabemodus geändert wird"""
        is_url_mode = self.url_mode_rb.isChecked()

        # URL-Widgets ein-/ausblenden
        for widget in self.url_widgets:
            widget.setVisible(is_url_mode)

        # Datei-Widgets ein-/ausblenden
        for widget in self.file_widgets:
            widget.setVisible(not is_url_mode)

        # Button-Text anpassen
        if hasattr(self, 'download_btn'):
            if is_url_mode:
                self.download_btn.setText("Herunterladen")
            else:
                self.download_btn.setText("Einsortieren")

        # Komprimierungs-Label anpassen
        if hasattr(self, 'auto_compress_cb'):
            if is_url_mode:
                self.auto_compress_cb.setText("Automatische Komprimierung (>0.9 MB, außer Twitter/Instagram)")
            else:
                self.auto_compress_cb.setText("Automatische Komprimierung (>0.9 MB)")

    def select_files(self):
        """Öffnet einen Dateidialog zur Auswahl von Bilddateien"""
        # Startverzeichnis setzen
        sort_dir = "/Users/<USER>/Pictures/Anime/Cosplay-sort"
        start_dir = sort_dir if Path(sort_dir).exists() else ""

        # Temporäre Menüleiste für macOS Tastenkürzel (Cmd+A für "Alle auswählen")
        menu_bar = QMenuBar()
        edit_menu = menu_bar.addMenu("Edit")
        edit_menu.addAction("Select All").setShortcut(QKeySequence.StandardKey.SelectAll)
        menu_bar.show()

        try:
            # Dialog-Instanz erstellen (nicht statische Methode verwenden)
            dialog = QFileDialog(self)
            dialog.setWindowTitle("Mediendateien auswählen")
            dialog.setDirectory(start_dir)
            dialog.setNameFilter("Mediendateien (*.jpg *.jpeg *.png *.gif *.bmp *.webp *.tiff *.tif *.mp4);;Alle Dateien (*)")
            dialog.setFileMode(QFileDialog.FileMode.ExistingFiles)

            if dialog.exec():
                selected_files = dialog.selectedFiles()
            else:
                selected_files = []

            if selected_files:
                for file_path in selected_files:
                    # Prüfen ob Datei bereits in der Liste ist
                    items = [self.selected_files_list.item(i).text() for i in range(self.selected_files_list.count())]
                    if file_path not in items:
                        self.selected_files_list.addItem(file_path)
        finally:
            # Menüleiste wieder verstecken
            menu_bar.hide()

    def clear_selected_files(self):
        """Leert die Liste der ausgewählten Dateien"""
        self.selected_files_list.clear()

    def get_selected_files(self):
        """Gibt die Liste der ausgewählten Dateien zurück"""
        files = []
        for i in range(self.selected_files_list.count()):
            files.append(self.selected_files_list.item(i).text())
        return files

    def parse_urls(self, text):
        """Extrahiert URLs aus einem Text. Akzeptiert durch Leerzeichen, Kommas oder Zeilenumbrüche getrennte URLs"""
        # Erst bei Zeilenumbrüchen, Kommas und Leerzeichen trennen
        parts = re.split(r'[\n,\s]+', text.strip())
        # Nur nicht-leere Teile zurückgeben
        return [part for part in parts if part]
    
    def update_history(self):
        """Aktualisiert die Cosplayer- und Charakter-Listen durch erneutes Scannen des Ordners."""
        # Nach dem Download den Ordner erneut scannen, um neue Namen zu erfassen
        self.load_history()
        self.update_completer_models()

    def update_completer_models(self):
        """Aktualisiert die Modelle für die QCompleter."""
        self.cosplayer_completer_model.setStringList(self.cosplayer_history)
        self.character_completer_model.setStringList(self.character_history)

    def extract_names_from_folder(self):
        """Extrahiert Cosplayer- und Charakternamen aus den Dateinamen im Cosplay-Ordner."""
        # Dictionaries to track case-insensitive names and preserve original case
        cosplayers_dict = {}  # lowercase_name -> original_name
        characters_dict = {}  # lowercase_name -> original_name

        try:
            if not COSPLAY_DIR.exists():
                self.log_widget.log_warning(f"Cosplay-Ordner existiert nicht: {COSPLAY_DIR}")
                return [], []

            # Regex-Pattern für Dateinamen
            # Gruppe 1: Cosplayer(s), Gruppe 2: Charakter(s) (optional)
            pattern = re.compile(r'^(.+?)(?:\s-\s\[(.+?)\])?(?:\s\d+)?\.[\w]+$', re.IGNORECASE)

            for file_path in COSPLAY_DIR.iterdir():
                if not file_path.is_file():
                    continue

                filename = file_path.name
                match = pattern.match(filename)

                if match:
                    cosplayer_part = match.group(1).strip()
                    character_part = match.group(2)

                    # Cosplayer(s) verarbeiten (durch Komma getrennt)
                    if cosplayer_part:
                        for cosplayer in cosplayer_part.split(','):
                            cosplayer = cosplayer.strip()
                            if cosplayer:
                                # Use lowercase as key for case-insensitive deduplication
                                cosplayer_lower = cosplayer.lower()
                                # Keep the first occurrence's case, or prefer non-lowercase versions
                                if cosplayer_lower not in cosplayers_dict or cosplayer != cosplayer_lower:
                                    cosplayers_dict[cosplayer_lower] = cosplayer

                    # Charakter(e) verarbeiten (durch Komma getrennt)
                    if character_part:
                        for character in character_part.split(','):
                            character = character.strip()
                            if character and not any(word.lower() in character.lower() for word in ['patreon', 'fantia']):
                                # Use lowercase as key for case-insensitive deduplication
                                character_lower = character.lower()
                                # Keep the first occurrence's case, or prefer non-lowercase versions
                                if character_lower not in characters_dict or character != character_lower:
                                    characters_dict[character_lower] = character

            # Extract values and sort
            cosplayer_list = sorted(list(cosplayers_dict.values()), key=str.lower)
            character_list = sorted(list(characters_dict.values()), key=str.lower)

            self.log_widget.log_info(f"Extrahiert: {len(cosplayer_list)} Cosplayer, {len(character_list)} Charaktere")
            return cosplayer_list, character_list

        except Exception as e:
            self.log_widget.log_error(f"Fehler beim Extrahieren der Namen: {e}")
            return [], []

    def show_cosplayer_history_popup(self):
        """Zeigt alle Einträge der Cosplayer-Historie im Completer-Popup an."""
        self.cosplayer_input.setFocus()
        self.cosplayer_completer.complete()

    def show_character_history_popup(self):
        """Zeigt alle Einträge der Charakter-Historie im Completer-Popup an."""
        self.character_input.setFocus()
        self.character_completer.complete()

    def load_history(self):
        """Lädt Cosplayer und Charaktere aus dem Cosplay-Ordner."""
        self.cosplayer_history, self.character_history = self.extract_names_from_folder()

    def scan_for_new_images(self):
        """Startet das Scannen des Cosplay-Ordners nach neuen Bildern im Hintergrund"""
        if self.image_scan_thread and self.image_scan_thread.isRunning():
            return  # Scan läuft bereits

        # Thread erstellen und starten
        self.image_scan_thread = ImageScanThread(self.hash_manager, COSPLAY_DIR)

        # Signale verbinden
        self.image_scan_thread.progress_signal.connect(self.on_scan_progress)
        self.image_scan_thread.status_signal.connect(self.on_scan_status)
        self.image_scan_thread.finished_signal.connect(self.on_scan_finished)
        self.image_scan_thread.db_cleaned_signal.connect(self.update_status_bar_info)  # DB-Bereinigungssignal

        # UI während des Scannens anpassen
        self.set_ui_enabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)

        # Thread starten
        self.image_scan_thread.start()

    def on_scan_progress(self, current, total):
        """Behandelt Fortschritts-Updates vom Scan-Thread"""
        if total > 0:
            progress = int((current / total) * 100)
            self.progress_bar.setValue(progress)
            self.statusBar().showMessage(f"Scanne Bilder: {current}/{total} ({progress}%)")
            self.update_dock_progress(current, total)

    def on_scan_status(self, message, msg_type):
        """Behandelt Status-Updates vom Scan-Thread"""
        if msg_type == "success":
            self.log_widget.log_success(message)
        elif msg_type == "error":
            self.log_widget.log_error(message)
        elif msg_type == "warning":
            self.log_widget.log_warning(message)
        else:
            self.log_widget.log_info(message)

    def on_scan_finished(self, scanned_count):
        """Behandelt das Ende des Scan-Vorgangs"""
        # UI wieder aktivieren
        self.set_ui_enabled(True)
        self.progress_bar.setVisible(False)
        self.statusBar().showMessage("Bereit")

        # Cosplayer-Textfeld fokussieren
        self.cosplayer_input.setFocus()

        # Dock-Badge entfernen
        self.clear_dock_progress()

        # Thread aufräumen
        if self.image_scan_thread:
            self.image_scan_thread.deleteLater()
            self.image_scan_thread = None

        # Statusleiste aktualisieren
        self.update_status_bar_info()
    
    def set_ui_enabled(self, enabled: bool):
        """Aktiviert oder deaktiviert alle UI-Elemente"""
        self.cosplayer_input.setEnabled(enabled)
        self.character_input.setEnabled(enabled)
        self.url_input.setEnabled(enabled)
        self.url_mode_rb.setEnabled(enabled)
        self.file_mode_rb.setEnabled(enabled)
        self.select_files_btn.setEnabled(enabled)
        self.clear_files_btn.setEnabled(enabled)
        self.selected_files_list.setEnabled(enabled)
        self.check_duplicates_cb.setEnabled(enabled)
        self.auto_compress_cb.setEnabled(enabled)
        self.force_download_cb.setEnabled(enabled)
        self.download_btn.setEnabled(enabled)
        self.clear_btn.setEnabled(enabled)

        # Auch die Buttons in der Cosplayer/Character-Eingabe deaktivieren
        if hasattr(self.cosplayer_input, 'history_btn'):
            self.cosplayer_input.history_btn.setEnabled(enabled)
        if hasattr(self.character_input, 'history_btn'):
            self.character_input.history_btn.setEnabled(enabled)

    def start_download(self):
        """Startet den Download-/Einsortier-Prozess"""
        # Tags verwenden – wenn keine Chips gesetzt sind, auf reinen Texteingabe zurückfallen
        cosplayers = self.cosplayer_input.tags or (
            [self.cosplayer_input.line_edit.text().strip()] if self.cosplayer_input.line_edit.text().strip() else []
        )
        characters = self.character_input.tags or (
            [self.character_input.line_edit.text().strip()] if self.character_input.line_edit.text().strip() else []
        )

        force_download = self.force_download_cb.isChecked()
        check_duplicates = self.check_duplicates_cb.isChecked()
        auto_compress = self.auto_compress_cb.isChecked()
        is_url_mode = self.url_mode_rb.isChecked()

        if not cosplayers:
            self.log_widget.log_error("Bitte gib den Namen des Cosplayers ein.")
            return

        # Je nach Modus URLs oder Dateien verarbeiten
        if is_url_mode:
            url_text = self.url_input.toPlainText().strip()
            raw_urls = self.parse_urls(url_text)
            # Doppelte URLs nach Normalisierung filtern
            urls = []
            seen = set()
            for u in raw_urls:
                norm = DownloaderThread.process_image_url_static(u)
                if norm not in seen:
                    seen.add(norm)
                    urls.append(u)
            if len(urls) < len(raw_urls):
                self.log_widget.log_info(f"{len(raw_urls)-len(urls)} doppelte URL(s) entfernt.")
            if not urls:
                self.log_widget.log_error("Bitte gib mindestens eine gültige URL ein.")
                return
            sources = urls
        else:
            # Lokale Dateien
            selected_files = self.get_selected_files()
            if not selected_files:
                self.log_widget.log_error("Bitte wähle mindestens eine Bilddatei aus.")
                return

            # Prüfen ob alle Dateien existieren
            valid_files = []
            for file_path in selected_files:
                if Path(file_path).exists():
                    valid_files.append(file_path)
                else:
                    self.log_widget.log_warning(f"Datei nicht gefunden: {file_path}")

            if not valid_files:
                self.log_widget.log_error("Keine gültigen Dateien gefunden.")
                return

            sources = valid_files
        
        # Prüfe auf bereits heruntergeladene URLs, wenn nicht erzwungen (nur bei URL-Modus)
        if not force_download and is_url_mode:
            existing_urls = [url for url in sources if self.url_history.contains(url)]
            if existing_urls:
                msg = QMessageBox()
                icon = QApplication.style().standardIcon(QStyle.StandardPixmap.SP_MessageBoxWarning)
                msg.setIconPixmap(icon.pixmap(64, 64))
                msg.setWindowTitle("Bereits heruntergeladen")

                if len(existing_urls) == 1:
                    msg.setText("Diese URL wurde bereits heruntergeladen:")
                    msg.setInformativeText(existing_urls[0])
                else:
                    msg.setText(f"{len(existing_urls)} URLs wurden bereits heruntergeladen.")
                    msg.setInformativeText("Möchtest du den Download trotzdem starten?")

                msg.setStandardButtons(
                    QMessageBox.StandardButton.Yes |
                    QMessageBox.StandardButton.No |
                    QMessageBox.StandardButton.Cancel
                )

                msg.setDefaultButton(QMessageBox.StandardButton.No)
                msg.button(QMessageBox.StandardButton.Yes).setText("Ja, alle herunterladen")
                msg.button(QMessageBox.StandardButton.No).setText("Nur neue herunterladen")

                result = msg.exec()

                if result == QMessageBox.StandardButton.Cancel:
                    return
                elif result == QMessageBox.StandardButton.No:
                    # Nur neue URLs behalten (Vergleich nach Normalisierung)
                    sources = [url for url in sources if not self.url_history.contains(url)]
                    if not sources:
                        self.log_widget.log_info("Keine neuen URLs zum Herunterladen gefunden.")
                        return
        
        # Historie aktualisieren
        self.update_history()

        # Log vor neuer Download-Runde leeren
        self.log_widget.clear()

        # UI-Elemente deaktivieren
        self.set_ui_enabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(len(sources))
        self.progress_bar.setValue(0)
        self.progress_bar.setMaximum(len(sources))

        # Download/Einsortierung starten
        if is_url_mode:
            self.log_widget.log_info(f"Starte Download von {len(sources)} Bild(ern)...")
        else:
            self.log_widget.log_info(f"Starte Einsortierung von {len(sources)} Datei(en)...")

        # Download-Thread starten
        self.downloader_thread = DownloaderThread(sources, cosplayers, characters, self.hash_manager, check_duplicates, auto_compress, is_url_mode)
        self.downloader_thread.progress_signal.connect(self.update_progress)
        self.downloader_thread.status_signal.connect(self.update_status)
        self.downloader_thread.finished_signal.connect(self.download_finished)
        self.downloader_thread.duplicate_found_signal.connect(self.handle_duplicate_found)
        
        # Signal für verarbeitete Dateien nur im Datei-Modus verbinden
        if not is_url_mode:
            self.downloader_thread.file_processed_signal.connect(self.remove_processed_file_from_list)

        self.downloader_thread.start()

    def remove_processed_file_from_list(self, file_path: str):
        """Entfernt eine verarbeitete Datei aus der QListWidget."""
        for i in range(self.selected_files_list.count()):
            item = self.selected_files_list.item(i)
            if item and item.text() == file_path:
                self.selected_files_list.takeItem(i)
                break
    
    def update_progress(self, current, total):
        """Aktualisiert den Fortschrittsbalken"""
        self.progress_bar.setValue(current)
        self.statusBar().showMessage(f"Verarbeite {current}/{total}")
        self.update_dock_progress(current, total)
    
    def update_status(self, message, msg_type):
        """Aktualisiert den Statustext"""
        if msg_type == "success":
            self.log_widget.log_success(message)
        elif msg_type == "error":
            self.log_widget.log_error(message)
        elif msg_type == "info":
            self.log_widget.log_info(message)
        elif msg_type == "warning":
            self.log_widget.log_warning(message)
    
    def download_finished(self, successful, total, successful_urls):
        """Wird aufgerufen, wenn der Download abgeschlossen ist"""
        # UI-Elemente wieder aktivieren
        self.set_ui_enabled(True)

        if successful == total:
            self.log_widget.log_success(f"Abgeschlossen! {successful}/{total} Bilder erfolgreich verarbeitet.")
        else:
            self.log_widget.log_warning(f"Abgeschlossen. {successful}/{total} Bilder erfolgreich verarbeitet.")

        self.statusBar().showMessage(f"Bereit. {successful}/{total} Bilder verarbeitet.")

        # Dock-Badge entfernen
        self.clear_dock_progress()

        # Nur erfolgreich heruntergeladene URLs zur Historie hinzufügen
        for url in successful_urls:
            self.url_history.add_url(url)

        if successful_urls:
            self.log_widget.log_info(f"{len(successful_urls)} URL(s) zur Download-Historie hinzugefügt.")

        # Statusleiste aktualisieren
        self.update_status_bar_info()

    def handle_duplicate_found(self, source, similar_paths, image_hash):
        """Behandelt gefundene Duplikate und fragt den Benutzer nach der gewünschten Aktion"""

        # Prüfen ob es sich um eine URL oder lokale Datei handelt
        is_url = self.downloader_thread.is_url_mode if self.downloader_thread else True

        # Dialog erstellen
        dialog = QDialog(self)
        dialog.setWindowTitle("Ähnliche Bilder gefunden")
        dialog.setModal(True)

        # Größe je nach Anzahl der Duplikate anpassen
        if len(similar_paths) == 1:
            dialog.resize(1000, 700)  # Breiter für Vergleich
        else:
            dialog.resize(1200, 700)  # Noch breiter für mehrere Bilder

        layout = QVBoxLayout(dialog)

        # Info-Text
        if is_url:
            info_label = QLabel(f"Für diese URL wurden {len(similar_paths)} ähnliche Bilder gefunden:")
        else:
            info_label = QLabel(f"Für diese Datei wurden {len(similar_paths)} ähnliche Bilder gefunden:")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # Quelle anzeigen (gekürzt)
        if is_url:
            source_display = source[:80] + "..." if len(source) > 80 else source
            source_label = QLabel(f"URL: {source_display}")
        else:
            source_display = Path(source).name
            source_label = QLabel(f"Datei: {source_display}")
        source_label.setStyleSheet("font-family: monospace; background-color: #f0f0f0; padding: 5px;")
        source_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        layout.addWidget(source_label)

        # Hauptvergleichsbereich
        comparison_layout = QHBoxLayout()

        # Linke Seite: Original-Bild (zu laden)
        original_widget = QWidget()
        original_layout = QVBoxLayout(original_widget)
        original_layout.setContentsMargins(10, 10, 10, 10)

        if is_url:
            original_header = QLabel("🔽 Zu ladendes Bild:")
        else:
            original_header = QLabel("🔽 Einzusortierende Datei:")
        original_header.setStyleSheet("font-weight: bold; font-size: 12pt; color: #2E7D32; margin-bottom: 10px;")
        original_layout.addWidget(original_header)

        # Original-Bild laden und anzeigen
        original_img_label = QLabel()
        original_img_label.setFixedSize(300, 300)
        original_img_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        original_img_label.setStyleSheet("border: 2px solid #2E7D32; background-color: #E8F5E8;")

        # Versuche das Original-Bild zu laden
        original_file_size = "Unbekannt"
        original_resolution = "Unbekannt"
        temp_preview_path = None

        try:
            if is_url:
                # Temporär herunterladen für Vorschau
                processed_url = DownloaderThread.process_image_url_for_download(source)
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
                }

                if "cdn.v2ph.com" in processed_url:
                    headers['Referer'] = 'https://www.v2ph.com/'

                if re.search(r'https?://([^.]+\.)*sinaimg\.cn', processed_url):
                    headers['Referer'] = 'https://weibo.com/'

                response = requests.get(processed_url, headers=headers, timeout=30, stream=True)
                response.raise_for_status()

                # Temporäre Datei für Vorschau
                with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
                    for chunk in response.iter_content(chunk_size=8192):
                        temp_file.write(chunk)
                    temp_preview_path = temp_file.name
            else:
                # Lokale Datei: Original direkt verwenden (keine Kopie nötig)
                temp_preview_path = source

            # Dateigröße ermitteln
            file_size_bytes = os.path.getsize(temp_preview_path)
            if file_size_bytes < 1024 * 1024:
                original_file_size = f"{file_size_bytes / 1024:.1f} KB".replace('.', ',')
            else:
                original_file_size = f"{file_size_bytes / (1024 * 1024):.1f} MB".replace('.', ',')

            # Bild laden und anzeigen
            pixmap = QPixmap(temp_preview_path)
            if not pixmap.isNull():
                # Auflösung ermitteln
                original_resolution = f"{pixmap.width()} × {pixmap.height()}"

                scaled_pixmap = pixmap.scaled(
                    300, 300,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )
                original_img_label.setPixmap(scaled_pixmap)
            else:
                original_img_label.setText("❌ Vorschau\nnicht verfügbar")

            # Temporäre Datei löschen
            if is_url and temp_preview_path:
                try:
                    os.unlink(temp_preview_path)
                except:
                    pass

        except Exception:
            original_img_label.setText("❌ Vorschau\nnicht verfügbar")
            original_img_label.setStyleSheet("border: 2px solid #C62828; background-color: #FFEBEE; color: #C62828;")

        original_layout.addWidget(original_img_label)

        # Dateiinfo unter Original
        original_info = QLabel(f"Neue URL\n📏 {original_resolution}\n💾 {original_file_size}")
        original_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        original_info.setStyleSheet("font-size: 10pt; color: #2E7D32; margin-top: 5px; padding: 5px; background-color: #E8F5E8;")
        original_layout.addWidget(original_info)

        comparison_layout.addWidget(original_widget)

        # Rechte Seite: Gefundene Duplikate
        duplicates_widget = QWidget()
        duplicates_layout = QVBoxLayout(duplicates_widget)
        duplicates_layout.setContentsMargins(10, 10, 10, 10)

        duplicates_header = QLabel(f"🔄 Gefundene Duplikate ({len(similar_paths)}):")
        duplicates_header.setStyleSheet("font-weight: bold; font-size: 12pt; color: #C62828; margin-bottom: 10px;")
        duplicates_layout.addWidget(duplicates_header)

        # Scroll-Bereich für Duplikate
        scroll_area = QScrollArea()
        scroll_widget = QWidget()

        if len(similar_paths) == 1:
            # Einzelnes Duplikat - vertikales Layout
            scroll_layout = QVBoxLayout(scroll_widget)
            scroll_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

            path = Path(similar_paths[0])
            if path.exists():
                # Duplikat-Container
                img_container = QWidget()
                img_layout = QVBoxLayout(img_container)
                img_layout.setContentsMargins(5, 5, 5, 5)

                # Klickbares Bild
                img_label = ClickableImageLabel(path)
                img_label.setFixedSize(300, 300)
                img_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                img_label.setStyleSheet("border: 2px solid #C62828; background-color: #FFEBEE;")

                # Dateiinfo ermitteln
                duplicate_file_size = "Unbekannt"
                duplicate_resolution = "Unbekannt"

                try:
                    # Dateigröße
                    file_size_bytes = path.stat().st_size
                    if file_size_bytes < 1024 * 1024:
                        duplicate_file_size = f"{file_size_bytes / 1024:.1f} KB".replace('.', ',')
                    else:
                        duplicate_file_size = f"{file_size_bytes / (1024 * 1024):.1f} MB".replace('.', ',')
                except:
                    pass

                pixmap = QPixmap(str(path))
                if not pixmap.isNull():
                    # Auflösung ermitteln
                    duplicate_resolution = f"{pixmap.width()} × {pixmap.height()}"

                    scaled_pixmap = pixmap.scaled(
                        300, 300,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    img_label.setPixmap(scaled_pixmap)
                else:
                    img_label.setText("❌ Bild konnte\nnicht geladen werden")
                    img_label.setStyleSheet("border: 2px solid #C62828; background-color: #FFEBEE; color: #C62828;")

                img_layout.addWidget(img_label)

                # Dateiname (markierbar)
                filename_label = QLabel(path.name)
                filename_label.setWordWrap(True)
                filename_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                filename_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
                filename_label.setStyleSheet("font-size: 11pt; margin: 5px; padding: 5px; background-color: #f0f0f0;")
                img_layout.addWidget(filename_label)

                # Dateiinfo (Auflösung und Größe)
                info_label = QLabel(f"📏 {duplicate_resolution}\n💾 {duplicate_file_size}")
                info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                info_label.setStyleSheet("font-size: 10pt; color: #C62828; margin: 3px; padding: 5px; background-color: #FFEBEE;")
                img_layout.addWidget(info_label)

                # Vollständiger Pfad (markierbar)
                path_label = QLabel(f"Pfad: {str(path)}")
                path_label.setWordWrap(True)
                path_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
                path_label.setStyleSheet("font-size: 9pt; color: #666; margin: 3px; padding: 3px;")
                img_layout.addWidget(path_label)

                scroll_layout.addWidget(img_container)
        else:
            # Mehrere Duplikate - Grid Layout (kompakter)
            scroll_layout = QGridLayout(scroll_widget)

            # Ähnliche Bilder anzeigen (max 6)
            for i, path_str in enumerate(similar_paths[:6]):
                try:
                    path = Path(path_str)
                    if not path.exists():
                        continue

                    # Bild-Container
                    img_container = QWidget()
                    img_layout = QVBoxLayout(img_container)
                    img_layout.setContentsMargins(3, 3, 3, 3)

                    # Klickbares Bild (kleiner für mehrere)
                    img_label = ClickableImageLabel(path)
                    img_label.setFixedSize(180, 180)
                    img_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    img_label.setStyleSheet("border: 2px solid #C62828; background-color: #FFEBEE;")

                    # Dateiinfo ermitteln
                    duplicate_file_size = "?"
                    duplicate_resolution = "?"

                    try:
                        # Dateigröße (kompakt für mehrere)
                        file_size_bytes = path.stat().st_size
                        if file_size_bytes < 1024 * 1024:
                            duplicate_file_size = f"{file_size_bytes / 1024:.0f}K"
                        else:
                            duplicate_file_size = f"{file_size_bytes / (1024 * 1024):.1f}M".replace('.', ',')
                    except:
                        pass

                    pixmap = QPixmap(str(path))
                    if not pixmap.isNull():
                        # Auflösung ermitteln (kompakt)
                        w, h = pixmap.width(), pixmap.height()
                        if w >= 1000 or h >= 1000:
                            duplicate_resolution = f"{w//100}×{h//100}00"  # z.B. "19×10" für 1920×1080
                        else:
                            duplicate_resolution = f"{w}×{h}"

                        scaled_pixmap = pixmap.scaled(
                            180, 180,
                            Qt.AspectRatioMode.KeepAspectRatio,
                            Qt.TransformationMode.SmoothTransformation
                        )
                        img_label.setPixmap(scaled_pixmap)
                    else:
                        img_label.setText("❌")
                        img_label.setStyleSheet("border: 2px solid #C62828; background-color: #FFEBEE; color: #C62828;")

                    img_layout.addWidget(img_label)

                    # Dateiname (markierbar)
                    filename_label = QLabel(path.name)
                    filename_label.setWordWrap(True)
                    filename_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    filename_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
                    filename_label.setMaximumWidth(180)
                    filename_label.setStyleSheet("font-size: 9pt; padding: 2px; background-color: #f0f0f0;")
                    img_layout.addWidget(filename_label)

                    # Kompakte Dateiinfo
                    info_label = QLabel(f"{duplicate_resolution} • {duplicate_file_size}")
                    info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    info_label.setMaximumWidth(180)
                    info_label.setStyleSheet("font-size: 8pt; color: #C62828; padding: 1px; background-color: #FFEBEE;")
                    img_layout.addWidget(info_label)

                    # Position im Grid (2 Spalten für bessere Nutzung des Platzes)
                    row = i // 2
                    col = i % 2
                    scroll_layout.addWidget(img_container, row, col)

                except Exception:
                    continue

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        duplicates_layout.addWidget(scroll_area)

        comparison_layout.addWidget(duplicates_widget)

        # Vergleichsbereich zum Hauptlayout hinzufügen
        layout.addLayout(comparison_layout)

        # Buttons für die Entscheidung
        button_box = QWidget()
        button_layout = QHBoxLayout(button_box)
        button_layout.setContentsMargins(0, 10, 0, 0)

        skip_btn = QPushButton("Dieses Bild überspringen")
        skip_btn.setStyleSheet("background-color: #FFF9C4;")
        skip_btn.clicked.connect(lambda: self.set_decision(source, "skip", dialog))

        skip_all_btn = QPushButton("Alle zukünftigen überspringen")
        skip_all_btn.setStyleSheet("background-color: #FFECB3;")
        skip_all_btn.clicked.connect(lambda: self.set_decision(source, "skip_all", dialog))

        overwrite_btn = QPushButton("Existierende löschen & dieses speichern")
        overwrite_btn.setStyleSheet("background-color: #FFCDD2;")
        overwrite_btn.clicked.connect(lambda: self.set_decision(source, "overwrite", dialog))

        save_btn = QPushButton("Trotzdem speichern (als Kopie)")
        save_btn.setStyleSheet("background-color: #C8E6C9;")
        save_btn.clicked.connect(lambda: self.set_decision(source, "save", dialog))

        button_layout.addWidget(skip_btn)
        button_layout.addWidget(skip_all_btn)
        button_layout.addStretch()
        button_layout.addWidget(overwrite_btn)
        button_layout.addWidget(save_btn)

        layout.addWidget(button_box)

        # Dialog ausführen
        dialog.exec()

    def set_decision(self, source, decision, dialog):
        """Setzt die Benutzerentscheidung und schließt den Dialog"""
        if self.downloader_thread:
            # Wenn "Alle überspringen" gewählt wurde, den Modus im Thread setzen
            if decision == "skip_all":
                self.downloader_thread.skip_all_mode = True
                # Die Entscheidung für das aktuelle Bild ist "skip"
                self.downloader_thread.user_decisions[DownloaderThread.process_image_url_static(source) if self.downloader_thread.is_url_mode else source] = "skip"
            else:
                self.downloader_thread.user_decisions[DownloaderThread.process_image_url_static(source) if self.downloader_thread.is_url_mode else source] = decision
        dialog.accept()

    def clear_fields(self):
        """Leert alle Eingabefelder"""
        self.url_input.clear()
        self.selected_files_list.clear()
        self.cosplayer_input.clear()
        self.character_input.clear()
        self.progress_bar.setVisible(False)
        self.log_widget.clear()
        self.cosplayer_input.setFocus()

def main():
    app = QApplication(sys.argv)

    # Set macOS Dock Icon if AppKit is available
    if APPKIT_AVAILABLE:
        try:
            icon_path = "/Users/<USER>/Pictures/Icons/macOS/dl-cos-gui.icns"
            if os.path.exists(icon_path):
                ns_app = NSApplication.sharedApplication()
                image = NSImage.alloc().initByReferencingFile_(icon_path)
                if image:
                    ns_app.setApplicationIconImage_(image)
                else:
                    print(f"Warnung: Dock-Icon konnte nicht geladen werden von: {icon_path}", file=sys.stderr)
            else:
                print(f"Warnung: Dock-Icon-Datei nicht gefunden: {icon_path}", file=sys.stderr)
        except Exception as e:
            print(f"Fehler beim Setzen des Dock-Icons: {e}", file=sys.stderr)

    app.setStyle("Fusion")  # Ein moderner Look
    
    # Fenster erstellen und anzeigen
    window = CosplayDownloaderApp()
    window.showMaximized()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nProgramm wurde beendet.")
